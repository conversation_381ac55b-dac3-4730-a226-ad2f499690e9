.search-page {
    background: var(--bg);
    color: var(--text);

    .card {
        background: var(--surface);
        border: 1px solid #1b2a38;
        border-radius: var(--card-radius);
        box-shadow: var(--shadow);
    }

    .results-head {
        .summary {
            font-weight: 800;
        }

        .controls mat-form-field {
            width: 160px;
            margin: 0;
        }
    }

    .grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
    }

    @media (max-width: 991.98px) {
        .results-head {
            gap: 10px;
        }
    }
}