.filters {
    background: var(--surface);
    border-radius: var(--card-radius);
    overflow: hidden;

    .mat-expansion-panel-header {
        background: var(--surface-2);
        color: var(--text);
    }

    .facet {
        display: flex;
        flex-direction: column;
        gap: 6px;
        padding: 10px 14px;
    }

    .count {
        color: var(--muted);
        margin-left: 6px;
    }

    .stars {
        color: #ffd54f;
        margin-right: 6px;
    }

    .chips {
        flex-wrap: wrap;
        flex-direction: row;
    }

    .chip {
        background: var(--surface-2);
        color: var(--text);
        border: 1px solid #2a3b4e;
        border-radius: 999px;
        padding: 4px 10px;
        margin: 4px;
        cursor: pointer;
    }

    .chip.active {
        background: #223141;
        border-color: #2e8fff;
    }
}