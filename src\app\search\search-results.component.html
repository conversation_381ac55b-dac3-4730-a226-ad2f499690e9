<app-header></app-header>

<main class="search-page">
    <div class="container-xxl py-4">
        <div class="row g-4">
            <!-- Sidebar -->
            <aside class="col-12 col-lg-3">
                <app-filters [baseSet]="baseSet()" [active]="filters()" (changed)="onFiltersChange($event)">
                </app-filters>
            </aside>

            <!-- Results -->
            <section class="col-12 col-lg-9">
                <div class="results-head card d-flex flex-wrap align-items-center justify-content-between p-3 mb-3">
                    <div class="summary">{{ resultsSummary() }}</div>
                    <div class="controls d-flex align-items-center gap-2">
                        <mat-form-field appearance="fill">
                            <mat-label>Sort</mat-label>
                            <mat-select [formControl]="sortControl">
                                <mat-option value="latest">Latest</mat-option>
                                <mat-option value="rating">Highest Rating</mat-option>
                                <mat-option value="reviews">Highest Reviewed</mat-option>
                                <mat-option value="az">A–Z</mat-option>
                                <mat-option value="za">Z–A</mat-option>
                            </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="fill">
                            <mat-label>Page size</mat-label>
                            <mat-select [formControl]="pageSizeControl">
                                <mat-option [value]="9">9</mat-option>
                                <mat-option [value]="12">12</mat-option>
                                <mat-option [value]="18">18</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>

                <!-- Grid -->
                <div class="grid">
                    @for (c of pageItems(); track c.id) {
                    <app-course-card [thumbnailUrl]="c.thumbnailUrl" [title]="c.title" [provider]="c.provider.name"
                        [rating]="c.rating" [reviewCount]="c.reviewCount" [enrollmentCount]="c.enrollmentCount"
                        [difficulty]="c.difficulty" [durationText]="c.durationText"
                        [badgeText]="isNewLaunch(c) ? 'New Launch' : undefined">
                    </app-course-card>
                    }
                </div>

                <!-- Pagination -->
                <div class="pager d-flex align-items-center justify-content-center gap-2 mt-3">
                    <button mat-stroked-button (click)="goto(page()-1)" [disabled]="page()<=1">Prev</button>
                    <span class="px-2 small text-muted">Page {{ page() }} / {{ totalPages() }}</span>
                    <button mat-stroked-button (click)="goto(page()+1)" [disabled]="page()>=totalPages()">Next</button>
                </div>
            </section>
        </div>
    </div>
</main>