import { Component, EventEmitter, Input, Output, inject, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MATERIAL } from '../../shared/material.imports';
import { MatExpansionModule } from '@angular/material/expansion';
import { CourseService, Course, Level } from '../../core/services/course.service';
import { UserService } from '../../core/services/user.service';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';

export interface Filters {
    durations: string[];            // buckets
    ratings: string[];              // "4.5+", "4.0+", "3.5+"
    published: string[];            // buckets
    levels: Level[];
    authors: number[];
    topics: string[];
}

@Component({
    selector: 'app-filters',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, MatExpansionModule, ...MATERIAL],
    templateUrl: './filters.component.html',
    styleUrls: ['./filters.component.scss']
})
export class FiltersComponent implements OnChanges {
    private cs = inject(CourseService);
    private users = inject(UserService);
    private fb = inject(FormBuilder);

    @Input() baseSet: Course[] = [];      // courses filtered by search term (pre-facet)
    @Input() active: Filters = { durations: [], ratings: [], published: [], levels: [], authors: [], topics: [] };
    @Output() changed = new EventEmitter<Filters>();

    authorsList: { id: number; fullName: string }[] = [];
    counts = {
        durations: new Map<string, number>(),
        ratings: new Map<string, number>(),
        published: new Map<string, number>(),
        levels: new Map<string, number>(),
        authors: new Map<number, number>(),
        topics: new Map<string, number>(),
    };

    form = this.fb.group({
        durations: this.fb.control<string[]>([]),
        ratings: this.fb.control<string[]>([]),
        published: this.fb.control<string[]>([]),
        levels: this.fb.control<string[]>([]),
        authors: this.fb.control<number[]>([]),
        topics: this.fb.control<string[]>([])
    });

    ngOnChanges(_: SimpleChanges): void {
        this.computeCounts();
        this.form.patchValue(this.active, { emitEvent: false });
    }

    ngOnInit() {
        this.users.getAuthors().subscribe(a => this.authorsList = a);
        this.form.valueChanges.subscribe(val => this.changed.emit(val as Filters));
    }

    private computeCounts() {
        const src = this.baseSet;

        // Durations
        const dKeys = ['lt1w', '1to4w', '1to3m', '3to6m', '6to12m', 'gt12m'];
        this.counts.durations = new Map(dKeys.map(k => [k, 0]));
        src.forEach(c => {
            const b = this.cs.durationBucket(c.durationText);
            this.counts.durations.set(b, (this.counts.durations.get(b) || 0) + 1);
        });

        // Ratings
        const rKeys = ['4.5+', '4.0+', '3.5+'];
        this.counts.ratings = new Map(rKeys.map(k => [k, 0]));
        src.forEach(c => {
            if (c.rating >= 4.5) this.counts.ratings.set('4.5+', (this.counts.ratings.get('4.5+') || 0) + 1);
            if (c.rating >= 4.0) this.counts.ratings.set('4.0+', (this.counts.ratings.get('4.0+') || 0) + 1);
            if (c.rating >= 3.5) this.counts.ratings.set('3.5+', (this.counts.ratings.get('3.5+') || 0) + 1);
        });

        // Published
        const pKeys = ['thisWeek', 'thisMonth', 'last6m', 'thisYear', 'older'];
        this.counts.published = new Map(pKeys.map(k => [k, 0]));
        src.forEach(c => {
            const b = this.cs.publishedBucket(c.publishedDate);
            this.counts.published.set(b, (this.counts.published.get(b) || 0) + 1);
        });

        // Levels
        const lKeys: Level[] = ['Beginner', 'Intermediate', 'Advanced'];
        this.counts.levels = new Map(lKeys.map(k => [k, 0]));
        src.forEach(c => this.counts.levels.set(c.difficulty, (this.counts.levels.get(c.difficulty) || 0) + 1));

        // Authors
        this.counts.authors = new Map<number, number>();
        src.forEach(c => this.counts.authors.set(c.authorId, (this.counts.authors.get(c.authorId) || 0) + 1));

        // Topics (skills)
        this.counts.topics = new Map<string, number>();
        src.forEach(c => (c.skills || []).forEach(s => {
            const k = s.trim();
            this.counts.topics.set(k, (this.counts.topics.get(k) || 0) + 1);
        }));
    }

    // Display labels
    labelDuration(k: string) {
        return ({
            lt1w: '< 1 week', '1to4w': '1–4 weeks', '1to3m': '1–3 months',
            '3to6m': '3–6 months', '6to12m': '6–12 months', gt12m: '> 12 months'
        } as any)[k] || k;
    }
    labelPublished(k: string) {
        return ({ thisWeek: 'This week', thisMonth: 'This month', last6m: 'Last 6 months', thisYear: 'This year', older: 'Older' } as any)[k] || k;
    }

    // Helper methods for checkbox changes
    toggleDuration(value: string) {
        const current = this.form.value.durations || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ durations: updated });
    }

    toggleRating(value: string) {
        const current = this.form.value.ratings || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ ratings: updated });
    }

    togglePublished(value: string) {
        const current = this.form.value.published || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ published: updated });
    }

    toggleLevel(value: string) {
        const current = this.form.value.levels || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ levels: updated });
    }

    toggleAuthor(value: number) {
        const current = this.form.value.authors || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ authors: updated });
    }

    toggleTopic(value: string) {
        const current = this.form.value.topics || [];
        const updated = current.includes(value)
            ? current.filter(x => x !== value)
            : [...current, value];
        this.form.patchValue({ topics: updated });
    }

    getTopicKeys(): string[] {
        return Array.from(this.counts.topics.keys());
    }
}
