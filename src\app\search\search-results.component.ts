import { Component, OnInit, inject, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { HeaderComponent } from '../shared/ui/header/header.component';
import { CourseService, Course } from '../core/services/course.service';
import { Filters, FiltersComponent } from './filters/filters.component';
import { CourseCardComponent } from '../shared/ui/course-card/course-card.component';
import { MATERIAL } from '../shared/material.imports';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

type SortKey = 'latest' | 'rating' | 'reviews' | 'az' | 'za';

@Component({
    selector: 'app-search-results',
    standalone: true,
    imports: [CommonModule, HeaderComponent, FiltersComponent, CourseCardComponent, ReactiveFormsModule, ...MATERIAL],
    templateUrl: './search-results.component.html',
    styleUrls: ['./search-results.component.scss']
})
export class SearchResultsComponent implements OnInit {
    private router = inject(Router);
    private route = inject(ActivatedRoute);
    private cs = inject(CourseService);

    q = signal<string>('');
    all = signal<Course[]>([]);
    baseSet = signal<Course[]>([]);  // filtered by q (for counts)
    filters = signal<Filters>({ durations: [], ratings: [], published: [], levels: [], authors: [], topics: [] });

    sortControl = new FormControl<SortKey>('latest', { nonNullable: true });
    pageSizeControl = new FormControl<number>(12, { nonNullable: true });
    page = signal(1);

    ngOnInit() {
        this.route.queryParamMap.subscribe(p => {
            this.q.set(p.get('q')?.trim() || '');
            this.load();
        });

        this.sortControl.valueChanges.subscribe(() => this.page.set(1));
        this.pageSizeControl.valueChanges.subscribe(() => this.page.set(1));
    }

    private load() {
        const term = this.q();
        const src$ = term ? this.cs.search(term) : this.cs.getAll();
        src$.subscribe(list => {
            this.all.set(list);
            this.baseSet.set(list); // pre-facet for counts
        });
    }

    onFiltersChange(f: Filters) {
        this.filters.set(f);
        this.page.set(1);
    }

    // Apply filters + sorting
    filtered = computed(() => {
        const f = this.filters();
        let set = this.all();

        if (f.durations.length) set = set.filter(c => f.durations.includes(this.cs.durationBucket(c.durationText)));
        if (f.ratings.length) {
            set = set.filter(c =>
                f.ratings.some(r => {
                    const min = parseFloat(r);
                    return c.rating >= min;
                })
            );
        }
        if (f.published.length) set = set.filter(c => f.published.includes(this.cs.publishedBucket(c.publishedDate)));
        if (f.levels.length) set = set.filter(c => f.levels.includes(c.difficulty));
        if (f.authors.length) set = set.filter(c => f.authors.includes(c.authorId));
        if (f.topics.length) set = set.filter(c => (c.skills || []).some(s => f.topics.includes(s)));

        // sort
        const k = this.sortControl.value;
        set = [...set];
        switch (k) {
            case 'latest': set.sort((a, b) => +new Date(b.publishedDate) - +new Date(a.publishedDate)); break;
            case 'rating': set.sort((a, b) => b.rating - a.rating); break;
            case 'reviews': set.sort((a, b) => b.reviewCount - a.reviewCount); break;
            case 'az': set.sort((a, b) => a.title.localeCompare(b.title)); break;
            case 'za': set.sort((a, b) => b.title.localeCompare(a.title)); break;
        }
        return set;
    });

    // Pagination
    totalPages = computed(() => Math.max(1, Math.ceil(this.filtered().length / this.pageSizeControl.value)));
    pageItems = computed(() => {
        const size = this.pageSizeControl.value;
        const start = (this.page() - 1) * size;
        return this.filtered().slice(start, start + size);
    });

    goto(p: number) {
        if (p < 1 || p > this.totalPages()) return;
        this.page.set(p);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    resultsSummary() {
        const n = this.filtered().length;
        const q = this.q();
        return q ? `${n} Results for “${q}”` : `${n} Courses`;
    }

    isNewLaunch(c: Course) {
        const days = (Date.now() - new Date(c.publishedDate).getTime()) / 86400000;
        return days <= 60;
    }
}
