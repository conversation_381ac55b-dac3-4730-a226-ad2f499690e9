import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';

export interface User { id: number; fullName: string; role?: string; }

@Injectable({ providedIn: 'root' })
export class UserService {
    private http = inject(HttpClient);
    private API = 'http://localhost:3000';

    getAuthors() {
        return this.http.get<User[]>(`${this.API}/users?role=Author`)
            .pipe(map(users => users.map(u => ({ id: u.id, fullName: u.fullName }))));
    }
}
