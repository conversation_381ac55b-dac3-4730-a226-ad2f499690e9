<mat-accordion class="filters">
    <!-- Duration -->
    <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>Duration</mat-expansion-panel-header>
        <div class="facet">
            @for (k of ['lt1w','1to4w','1to3m','3to6m','6to12m','gt12m']; track k) {
            <mat-checkbox [value]="k" [checked]="form.value.durations?.includes(k)" (change)="toggleDuration(k)">
                {{ labelDuration(k) }} <span class="count">({{ counts.durations.get(k) || 0 }})</span>
            </mat-checkbox>
            }
        </div>
    </mat-expansion-panel>

    <!-- Rating -->
    <mat-expansion-panel>
        <mat-expansion-panel-header>Rating</mat-expansion-panel-header>
        <div class="facet">
            @for (k of ['4.5+','4.0+','3.5+']; track k) {
            <mat-checkbox [checked]="form.value.ratings?.includes(k)" (change)="toggleRating(k)">
                <span class="stars">★</span> {{ k }} <span class="count">({{ counts.ratings.get(k)||0 }})</span>
            </mat-checkbox>
            }
        </div>
    </mat-expansion-panel>

    <!-- Published Date -->
    <mat-expansion-panel>
        <mat-expansion-panel-header>Published Date</mat-expansion-panel-header>
        <div class="facet">
            @for (k of ['thisWeek','thisMonth','last6m','thisYear','older']; track k) {
            <mat-checkbox [checked]="form.value.published?.includes(k)" (change)="togglePublished(k)">
                {{ labelPublished(k) }} <span class="count">({{ counts.published.get(k)||0 }})</span>
            </mat-checkbox>
            }
        </div>
    </mat-expansion-panel>

    <!-- Course Level -->
    <mat-expansion-panel>
        <mat-expansion-panel-header>Course Level</mat-expansion-panel-header>
        <div class="facet">
            @for (k of ['Beginner','Intermediate','Advanced']; track k) {
            <mat-checkbox [checked]="form.value.levels?.includes(k)" (change)="toggleLevel(k)">
                {{ k }} <span class="count">({{ counts.levels.get(k)||0 }})</span>
            </mat-checkbox>
            }
        </div>
    </mat-expansion-panel>

    <!-- Author -->
    <mat-expansion-panel>
        <mat-expansion-panel-header>Author</mat-expansion-panel-header>
        <div class="facet">
            @for (a of authorsList; track a.id) {
            <mat-checkbox [checked]="form.value.authors?.includes(a.id)" (change)="toggleAuthor(a.id)">
                {{ a.fullName }} <span class="count">({{ counts.authors.get(a.id)||0 }})</span>
            </mat-checkbox>
            }
        </div>
    </mat-expansion-panel>

    <!-- Topics -->
    <mat-expansion-panel>
        <mat-expansion-panel-header>Topics</mat-expansion-panel-header>
        <div class="facet chips">
            @for (t of getTopicKeys(); track t) {
            <button type="button" class="chip" [class.active]="form.value.topics?.includes(t)" (click)="toggleTopic(t)">
                {{ t }} <span class="count">({{ counts.topics.get(t)||0 }})</span>
            </button>
            }
        </div>
    </mat-expansion-panel>
</mat-accordion>