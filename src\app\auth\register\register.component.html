<app-two-column-auth title="LearnHub">
  <h2 class="headline">Sign up</h2>
  <p class="sub">Enter details mentioned below</p>

  <form [formGroup]="form" (ngSubmit)="submit()">
    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>Full Name</mat-label>
      <input matInput placeholder="Enter your full name" formControlName="fullName" />
      @if (form.controls.fullName.invalid) {
        <mat-error>Enter your name</mat-error>
      }
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>User Name</mat-label>
      <input matInput placeholder="Choose a username" formControlName="username" />
      @if (form.controls.username.invalid) {
        <mat-error>Username required</mat-error>
      }
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>Email</mat-label>
      <input matInput placeholder="Enter your email address" formControlName="email" />
      @if (form.controls.email.invalid) {
        <mat-error>Valid email required</mat-error>
      }
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-100 mb-2">
      <mat-label>Password</mat-label>
      <input matInput placeholder="Enter your password" [type]="hide() ? 'password' : 'text'" formControlName="password" />
      <button type="button" mat-icon-button matSuffix (click)="toggle()">
        <mat-icon>{{ hide() ? 'visibility_off' : 'visibility' }}</mat-icon>
      </button>
      @if (form.controls.password.invalid) {
        <mat-error>Min 6 characters</mat-error>
      }
    </mat-form-field>

    <mat-checkbox formControlName="agree" class="mb-3">
      I agree to the terms and conditions by signing up
    </mat-checkbox>

    <button mat-raised-button color="primary" class="btn-action w-100" [disabled]="loading() || form.invalid">
      @if (loading()) {
        <mat-progress-spinner diameter="18" mode="indeterminate"></mat-progress-spinner>
      }
      @if (!loading()) {
        <span>Register</span>
      }
    </button>

    <p class="mt-3 small">Already have an Account? <a routerLink="/auth/login" class="link">Login</a></p>
  </form>
</app-two-column-auth>
